# Настройки сервера
HTTP_PORT=4000
WS_PORT=9090

# Настройки базы данных PostgreSQL
DB_USER=postgres
DB_HOST=***********
DB_NAME=esp32_db
DB_PASSWORD=Fast777
DB_PORT=5432

# Настройки TON API
TON_API_URL=https://toncenter.com/api/v2
TON_API_KEY=your_ton_api_key_here

# Адрес смарт-контракта маркетплейса (устанавливается после деплоя)
MARKETPLACE_CONTRACT_ADDRESS=

# Интервал синхронизации транзакций (в миллисекундах)
SYNC_INTERVAL=30000

# Настройки логирования
LOG_LEVEL=info
