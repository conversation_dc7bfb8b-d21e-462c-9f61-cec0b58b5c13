-- Создание таблиц для интеграции смарт-контракта Marketplace
-- Выполнить: node run_sql.js sql/create_marketplace_tables.sql

-- 1. Табли<PERSON><PERSON> продавцов (sellers)
CREATE TABLE IF NOT EXISTS sellers (
    id SERIAL PRIMARY KEY,
    seller_id VARCHAR(100) NOT NULL UNIQUE, -- Уникальный ID продавца (например, "flowershop7")
    wallet_address TEXT NOT NULL UNIQUE,    -- TON-адрес кошелька продавца
    display_name VARCHAR(255),              -- Отображаемое имя продавца
    description TEXT,                       -- Описание продавца/магазина
    avatar_url TEXT,                        -- Ссылка на аватар
    contact_info JSONB,                     -- Контактная информация (email, telegram, etc.)
    is_active BOOLEAN DEFAULT true,         -- Активен ли продавец
    registration_tx_hash TEXT,              -- Хэш транзакции регистрации в смарт-контракте
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Таблица товаров/услуг (products)
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    product_id VARCHAR(100) NOT NULL,       -- ID товара (например, "roses_small")
    seller_id VARCHAR(100) NOT NULL,        -- ID продавца (связь с sellers.seller_id)
    title VARCHAR(255) NOT NULL,            -- Название товара
    description TEXT,                       -- Описание товара
    price_ton NUMERIC(18, 9) NOT NULL,      -- Цена в TON
    price_usd NUMERIC(10, 2),               -- Цена в USD (для отображения)
    category VARCHAR(100),                  -- Категория товара
    images JSONB,                           -- Массив ссылок на изображения
    is_active BOOLEAN DEFAULT true,         -- Активен ли товар
    stock_quantity INTEGER DEFAULT -1,      -- Количество на складе (-1 = неограниченно)
    digital_content JSONB,                  -- Цифровой контент (для цифровых товаров)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(seller_id, product_id),          -- Уникальность product_id в рамках продавца
    FOREIGN KEY (seller_id) REFERENCES sellers(seller_id) ON DELETE CASCADE
);

-- 3. Таблица заказов/покупок (orders)
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(100) NOT NULL UNIQUE,  -- Уникальный ID заказа
    buyer_wallet TEXT NOT NULL,             -- Адрес кошелька покупателя
    seller_id VARCHAR(100) NOT NULL,        -- ID продавца
    product_id VARCHAR(100) NOT NULL,       -- ID товара
    quantity INTEGER DEFAULT 1,             -- Количество
    price_ton NUMERIC(18, 9) NOT NULL,      -- Цена за единицу в TON
    total_amount_ton NUMERIC(18, 9) NOT NULL, -- Общая сумма в TON
    seller_share_ton NUMERIC(18, 9) NOT NULL, -- Доля продавца (90%)
    service_fee_ton NUMERIC(18, 9) NOT NULL,  -- Комиссия сервиса (10%)
    
    -- Статусы заказа
    status VARCHAR(50) DEFAULT 'pending',    -- pending, confirmed, completed, cancelled
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, withdrawn
    
    -- Информация о транзакциях
    purchase_tx_hash TEXT,                   -- Хэш транзакции покупки
    purchase_tx_lt BIGINT,                   -- Logical time транзакции
    withdraw_tx_hash TEXT,                   -- Хэш транзакции вывода продавцом
    
    -- Дополнительная информация
    buyer_info JSONB,                        -- Информация о покупателе (имя, контакты)
    delivery_info JSONB,                     -- Информация о доставке
    notes TEXT,                              -- Заметки к заказу
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (seller_id) REFERENCES sellers(seller_id) ON DELETE CASCADE
);

-- 4. Таблица балансов продавцов (seller_balances)
CREATE TABLE IF NOT EXISTS seller_balances (
    id SERIAL PRIMARY KEY,
    seller_id VARCHAR(100) NOT NULL,
    product_id VARCHAR(100) NOT NULL,
    balance_nanoton BIGINT DEFAULT 0,       -- Баланс в нанотонах
    balance_ton NUMERIC(18, 9) DEFAULT 0,   -- Баланс в TON (для удобства)
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(seller_id, product_id),
    FOREIGN KEY (seller_id) REFERENCES sellers(seller_id) ON DELETE CASCADE
);

-- 5. Таблица транзакций (transactions)
CREATE TABLE IF NOT EXISTS marketplace_transactions (
    id SERIAL PRIMARY KEY,
    tx_hash TEXT NOT NULL UNIQUE,           -- Хэш транзакции
    tx_lt BIGINT NOT NULL,                  -- Logical time
    tx_type VARCHAR(50) NOT NULL,           -- purchase, withdraw, register_seller
    
    -- Участники транзакции
    from_address TEXT NOT NULL,             -- Отправитель
    to_address TEXT NOT NULL,               -- Получатель (контракт или продавец)
    
    -- Данные транзакции
    amount_nanoton BIGINT,                  -- Сумма в нанотонах
    amount_ton NUMERIC(18, 9),              -- Сумма в TON
    
    -- Связанные данные
    seller_id VARCHAR(100),                 -- ID продавца (если применимо)
    product_id VARCHAR(100),                -- ID товара (если применимо)
    order_id VARCHAR(100),                  -- ID заказа (если применимо)
    
    -- Статус обработки
    processed BOOLEAN DEFAULT false,        -- Обработана ли транзакция
    error_message TEXT,                     -- Сообщение об ошибке (если есть)
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE SET NULL
);

-- 6. Таблица настроек контракта (contract_settings)
CREATE TABLE IF NOT EXISTS marketplace_contract_settings (
    id SERIAL PRIMARY KEY,
    contract_address TEXT NOT NULL UNIQUE,  -- Адрес смарт-контракта
    owner_address TEXT NOT NULL,            -- Адрес владельца контракта
    service_fee_percent INTEGER DEFAULT 10, -- Процент комиссии сервиса
    is_active BOOLEAN DEFAULT true,         -- Активен ли контракт
    deployed_at TIMESTAMP,                  -- Время деплоя
    last_sync TIMESTAMP,                    -- Последняя синхронизация с блокчейном
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Создание индексов для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_sellers_wallet_address ON sellers(wallet_address);
CREATE INDEX IF NOT EXISTS idx_products_seller_id ON products(seller_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_orders_buyer_wallet ON orders(buyer_wallet);
CREATE INDEX IF NOT EXISTS idx_orders_seller_id ON orders(seller_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_tx_hash ON marketplace_transactions(tx_hash);
CREATE INDEX IF NOT EXISTS idx_transactions_from_address ON marketplace_transactions(from_address);
CREATE INDEX IF NOT EXISTS idx_transactions_tx_type ON marketplace_transactions(tx_type);
CREATE INDEX IF NOT EXISTS idx_transactions_processed ON marketplace_transactions(processed);

-- Комментарии к таблицам
COMMENT ON TABLE sellers IS 'Продавцы в маркетплейсе';
COMMENT ON TABLE products IS 'Товары и услуги продавцов';
COMMENT ON TABLE orders IS 'Заказы/покупки в маркетплейсе';
COMMENT ON TABLE seller_balances IS 'Балансы продавцов по товарам';
COMMENT ON TABLE marketplace_transactions IS 'Транзакции смарт-контракта';
COMMENT ON TABLE marketplace_contract_settings IS 'Настройки смарт-контракта маркетплейса';
