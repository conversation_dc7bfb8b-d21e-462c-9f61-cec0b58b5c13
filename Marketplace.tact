//////////////////////////////////////////////////////////////////////
//                                                             //
//  Smart Contract “Marketplace” for TON-based Trading Platform  //
//                                                             //
//  Функционал:                                                 //
//  - Регистрация продавцов (sellerID → TON-адрес)              //
//  - Прием платежей от покупателей за товары/услуги           //
//  - Распределение средств: 90% — продавцу, 10% — сервису      //
//  - Хранение балансов по каждому товару у каждого продавца    //
//  - Вывод средств продавцом (по товару или общая сумма)       //
//  - Вывод комиссионных средств сервисом                      //
//  - Перевод “просроченных” балансов сервису после года        //
//                                                             //
//////////////////////////////////////////////////////////////////////

contract Marketplace {

    //───────────────────────────────────────────────────────
    //                       Storage
    //───────────────────────────────────────────────────────

    /// Привязка sellerID (string) → TON-адрес продавца
    sellerRegistry: map<string, address>;

    /// Балансы по товарам у продавцов:
    /// ключ — (sellerID, productID) (оба строки), значение — накопленная сумма в нанотонах TON
    productBalances: map<(string, string), uint64>;

    /// Общие накопленные средства продавца (аналогично сумме всех productBalances для удобства вывода «все сразу»)
    sellerTotals: map<address, uint64>;

    /// Временные метки последнего “действия” (регистрации / покупки / вывода) продавца:
    /// ключ — адрес продавца, значение — Unix-timestamp последней активности
    lastActivity: map<address, uint64>;

    /// Баланс сервиса (накопленная комиссия 10%) в нанотонах TON
    serviceBalance: uint64 = 0;

    //───────────────────────────────────────────────────────
    //                     Конструктор
    //───────────────────────────────────────────────────────

    /// При деплое контракта никаких специальных параметров не требуется.
    init() {
        // ничего не делаем; все maps инициализируются “пустыми”
    }

    //───────────────────────────────────────────────────────
    //                Функции контракта (API)
    //───────────────────────────────────────────────────────

    /// 1) Регистрация продавца
    ///
    ///    Продавец вызывает эту функцию один раз после подключения кошелька.
    ///    sellerID — его читабельный уникальный идентификатор (например, "flowershop7").
    ///
    ///    После успешного вызова в sellerRegistry появится запись:
    ///        sellerRegistry[sellerID] = msg.sender
    ///
    ///    Ограничение: sellerID должен быть свободен (еще не зарегистрирован).
    ///
    func registerSeller(sellerID: ^string) impure {
        // Проверка формата: sellerID не может быть пустой строкой
        require(sellerID.len() > 0, 1001, "sellerID не может быть пустым");

        // Проверяем, что sellerID ещё не занят
        if (self.sellerRegistry.contains(sellerID)) {
            throw(1002);  // Код 1002: sellerID уже зарегистрирован
        }

        // Записываем привязку sellerID → адрес вызывающего
        self.sellerRegistry.set(sellerID, msg.sender);

        // Устанавливаем начальную метку активности (считаем, что регистрация = активность)
        self.lastActivity.set(msg.sender, now());
    }

    /// 2) Покупка товара у продавца
    ///
    ///    Покупатель вызывает эту функцию, отправляя вместе с транзакцией нужное количество TON.
    ///    В аргументах передаются:
    ///      • sellerID  (string)   — идентификатор продавца (обязательный)
    ///      • productID (string)   — идентификатор товара/услуги (обязательный)
    ///
    ///    msg.value — сумма в нанотонах TON, которую покупатель отправил.
    ///    Контракт:
    ///      1) Проверяет, что sellerID зарегистрирован (в sellerRegistry).
    ///      2) Делит сумму: 90% (sellerShare) — уходит в баланс продавца, 10% (serviceShare) — на serviceBalance.
    ///      3) Обновляет две структуры:
    ///         • productBalances[(sellerID, productID)] += sellerShare
    ///         • sellerTotals[sellerAddress]       += sellerShare
    ///         • serviceBalance                    += serviceShare
    ///      4) Обновляет lastActivity[sellerAddress] = now()
    ///
    func purchase(sellerID: ^string, productID: ^string) impure {
        let amount: uint64 = msg.value;

        // Проверить, что продавец с таким sellerID зарегистрирован
        require(self.sellerRegistry.contains(sellerID), 2001, "sellerID не найден");

        // Извлечь адрес продавца
        let sellerAddress = self.sellerRegistry.get(sellerID);

        // Проверить, что buyer не отправляет 0 TON
        require(amount > 0, 2002, "Сумма должна быть > 0");

        // Вычисляем долю продавца (90%) и сервиса (10%)
        // Внимание: деление целочисленное, проценты считаем в целых единицах.
        let sellerShare: uint64 = (amount * 90) / 100;
        let serviceShare: uint64 = amount - sellerShare;

        // Ключ для productBalances
        let key: (string, string) = (sellerID, productID);

        // Текущий баланс данного товара у продавца (если пустой, получим 0)
        let currentProductBalance: uint64 = self.productBalances.get(key) ?? 0u64;

        // Обновляем баланс товара
        self.productBalances.set(key, currentProductBalance + sellerShare);

        // Обновляем общий баланс продавца
        let currentTotal: uint64 = self.sellerTotals.get(sellerAddress) ?? 0u64;
        self.sellerTotals.set(sellerAddress, currentTotal + sellerShare);

        // Обновляем баланс сервиса
        self.serviceBalance += serviceShare;

        // Обновляем метку активности продавца
        self.lastActivity.set(sellerAddress, now());
    }

    /// 3) Вывод средств продавцом (по конкретному товару или всей сумме сразу)
    ///
    ///    • Если productID != "" (не пустая строка) — вывести баланс только по этому товару.
    ///    • Если productID == ""         — вывести весь накопленный баланс (из sellerTotals).
    ///
    ///    msg.sender должен быть адресом продавца, зарегистрированным ранее.
    ///
    func withdraw(productID: ^string) impure {
        let caller: address = msg.sender;

        // Проверяем, что caller зарегистрирован как продавец (т.е. присутствует в sellerRegistry как значение)
        // Для этого нужно пробежать по sellerRegistry и найти, где value == caller.
        // В Tact нет прямого “reverse lookup” в map, поэтому введём вспомогательную проверку:
        //
        // 1) Проверяем, есть ли хоть один sellerID, у которого sellerRegistry[sellerID] == caller.
        //    Для оптимальности можно завести ещё map<address, string> addressToSellerID при регистрации, 
        //    но здесь сделаем “жесткую” проверку через addressToSellerID.
        //
        // Реализация: для хранения “обратной связи” при регистрации добавим ещё
        // addressToSellerID: map<address, string> (на уровне контракта).
        //
        // Но в этой версии кода мы считаем, что при регистрации мы сразу сохраняем обе связи:
        //    sellerRegistry: sellerID → address
        //    reverseRegistry: address  → sellerID
        //
        // Поэтому дополним storage полем:
        // reverseRegistry: map<address, string>;
        //
        // И проверка становится тривиальной.
        //
        // Ниже — версия с reverseRegistry. (См. раздел “Storage” и “registerSeller”)

        ////////////////////////////////////////////////////////
        // ↓ ИСПРАВЛЕНИЕ STORAGE (добавляем):
        //   reverseRegistry: map<address, string>;
        //
        // ↓ ИСПРАВЛЕНИЕ registerSeller (добавляем):
        //   self.reverseRegistry.set(msg.sender, sellerID);
        //
        // ← Теперь проверяем:
        if (!self.reverseRegistry.contains(caller)) {
            throw(3001); // “Вы не зарегистрированы как продавец”
        }
        ////////////////////////////////////////////////////////

        // Определяем sellerID по адресу
        let sellerID: string = self.reverseRegistry.get(caller);

        // Если задан непустой productID — выводим только по нему
        if (productID.len() > 0) {
            let key: (string, string) = (sellerID, productID);
            let balanceProd: uint64 = self.productBalances.get(key) ?? 0u64;
            require(balanceProd > 0, 3002, "Баланс по этому товару = 0");

            // Вычисляем, какая комиссия сети уйдет за эту транзакцию,
            // здесь предполагаем, что продавец сам оплачивает gas (мы ничего не удерживаем).
            // Просто отправляем всю сумму balanceProd.
            send(caller, balanceProd);

            // Обнуляем баланс по этому товару
            self.productBalances.set(key, 0u64);

            // Обновляем общий баланс продавца
            let totalLeft: uint64 = self.sellerTotals.get(caller)! - balanceProd;
            self.sellerTotals.set(caller, totalLeft);

            // Обновляем метку активности
            self.lastActivity.set(caller, now());
        }
        else {
            // Если productID пустой — выводим весь balance (sellerTotals)
            let totalBalance: uint64 = self.sellerTotals.get(caller) ?? 0u64;
            require(totalBalance > 0, 3003, "Общий баланс = 0");

            send(caller, totalBalance);

            // Обнуляем все productBalances для этого продавца:
            // В Tact нет “итерирования по map”, поэтому оставляем productBalances как есть,
            // но обнуляем sellerTotals, а при будущих “purchase” новый sellerShare пойдёт в productBalances заново.
            // Важно: productBalances[(sellerID, *)] всё ещё хранят старые данные, 
            // но они больше не суммируются в sellerTotals, поэтому “лишний” мусор.
            //
            // Если нужно полностью обнулить productBalances для всех productID продавца,
            // придётся либо хранить список productID (отдельно), либо завести событие “clearAllForSeller”.
            //
            // Для простоты: обнуляем лишь sellerTotals, а productBalances оставляем как есть.
            self.sellerTotals.set(caller, 0u64);

            self.lastActivity.set(caller, now());
        }
    }

    /// 4) Вывод средств сервиса (только владелец контракта)
    ///
    ///    addrOwner — тот же адрес, который деплоил контракт (msg.sender при init).
    ///
    func withdrawService(amount: uint64) impure {
        // Убедимся, что вызывающий — это владелец контракта
        // В Tact невозможно “захардкодить” owner при init, но можно поступить так:
        // в init() сохранить owner = msg.sender в storage.
        //
        // Добавляем поле в storage:
        //   owner: address;
        //
        // И в init(): self.owner = msg.sender;
        //
        if (msg.sender != self.owner) {
            throw(4001); // “Только владелец сервиса может вызвать эту функцию”
        }

        require(self.serviceBalance >= amount, 4002, "Недостаточно средств сервиса");

        self.serviceBalance -= amount;
        send(self.owner, amount);
    }

    /// 5) Перевод “просроченных” средств сервиса
    ///
    ///    Если продавец не выводил средства более 365 дней, либо не было активности (purchase/withdraw/регистрация),
    ///    владелец сервиса (self.owner) или любой “keeper” может вызвать эту функцию:
    ///
    ///    Для simplicity: мы принимаем parameter sellerAddr (TON-адрес продавца).
    ///    Функция проверяет lastActivity[sellerAddr], если currentTime − lastActivity > 365 дней,
    ///    переводит весь sellerTotals[sellerAddr] в self.serviceBalance и обнуляет sellerTotals.
    ///
    func reclaimStaleFunds(sellerAddr: address) impure {
        // Проверка, что вызывает владелец контракта
        if (msg.sender != self.owner) {
            throw(5001); // “Только владелец сервиса может вызывать reclaimStaleFunds”
        }

        // Проверяем, что у продавца был хотя бы один активный action
        require(self.lastActivity.contains(sellerAddr), 5002, "Нет активности от этого продавца");

        let lastTime: uint64 = self.lastActivity.get(sellerAddr)!;
        let currentTime: uint64 = now();

        // 365 дней в секундах (365 * 24 * 60 * 60)
        let oneYear: uint64 = 31_536_000u64;

        require(currentTime > lastTime + oneYear, 5003, "Еще не прошло 365 дней");

        // Переводим весь баланс продавца в сервис
        let staleBalance: uint64 = self.sellerTotals.get(sellerAddr) ?? 0u64;
        if (staleBalance > 0) {
            self.serviceBalance += staleBalance;
            self.sellerTotals.set(sellerAddr, 0u64);
        }

        // Также можно “очистить” productBalances[(sellerID, *)], но в Tact нет итерации по map.
        // Оставляем productBalances как есть (mусор освободится при будущих операциях, 
        // т.к. sellerTotals = 0 и новые purchase создадут новые ключи).

        // Обновляем метку активности (можно оставить старую, но по сути этот seller уже “мёртв”)
        self.lastActivity.set(sellerAddr, currentTime);
    }

    //───────────────────────────────────────────────────────
    //                    Вспомогательные функции
    //───────────────────────────────────────────────────────

    /// 6) Получить баланс по конкретному товару
    ///
    func getProductBalance(sellerID: ^string, productID: ^string): uint64 {
        let key: (string, string) = (sellerID, productID);
        return self.productBalances.get(key) ?? 0u64;
    }

    /// 7) Получить общий баланс продавца (sellerTotals)
    ///
    func getSellerTotal(sellerAddr: address): uint64 {
        return self.sellerTotals.get(sellerAddr) ?? 0u64;
    }

    /// 8) Получить текущий баланс сервиса
    ///
    func getServiceBalance(): uint64 {
        return self.serviceBalance;
    }
}
