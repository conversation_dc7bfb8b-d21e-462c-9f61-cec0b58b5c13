/**
 * Сервис для работы с TON блокчейном и смарт-контрактом Marketplace
 */

const axios = require('axios');
const { Pool } = require('pg');

class TonService {
    constructor(pool, logger) {
        this.pool = pool;
        this.logger = logger;
        
        // Настройки TON API
        this.tonApiUrl = process.env.TON_API_URL || 'https://toncenter.com/api/v2';
        this.tonApiKey = process.env.TON_API_KEY || '';
        
        // Адрес смарт-контракта Marketplace (устанавливается после деплоя)
        this.contractAddress = process.env.MARKETPLACE_CONTRACT_ADDRESS || '';
        
        // Интервал синхронизации транзакций (в миллисекундах)
        this.syncInterval = parseInt(process.env.SYNC_INTERVAL) || 30000; // 30 секунд
        
        this.isRunning = false;
    }

    /**
     * Запуск мониторинга транзакций
     */
    startMonitoring() {
        if (this.isRunning) {
            this.logger.warn('TON мониторинг уже запущен');
            return;
        }

        this.isRunning = true;
        this.logger.info('🚀 Запуск мониторинга TON транзакций');
        
        // Запускаем периодическую синхронизацию
        this.syncIntervalId = setInterval(() => {
            this.syncTransactions().catch(error => {
                this.logger.error('Ошибка синхронизации транзакций:', error);
            });
        }, this.syncInterval);

        // Первая синхронизация сразу
        this.syncTransactions().catch(error => {
            this.logger.error('Ошибка первичной синхронизации:', error);
        });
    }

    /**
     * Остановка мониторинга
     */
    stopMonitoring() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        if (this.syncIntervalId) {
            clearInterval(this.syncIntervalId);
        }
        this.logger.info('⏹️ Мониторинг TON транзакций остановлен');
    }

    /**
     * Синхронизация транзакций с блокчейном
     */
    async syncTransactions() {
        if (!this.contractAddress) {
            this.logger.warn('Адрес контракта не установлен, пропускаем синхронизацию');
            return;
        }

        try {
            // Получаем последнюю обработанную транзакцию
            const lastTxResult = await this.pool.query(`
                SELECT tx_lt FROM marketplace_transactions 
                WHERE processed = true 
                ORDER BY tx_lt DESC 
                LIMIT 1
            `);

            const lastLt = lastTxResult.rows.length > 0 ? lastTxResult.rows[0].tx_lt : 0;

            // Получаем новые транзакции из блокчейна
            const transactions = await this.getContractTransactions(lastLt);
            
            if (transactions.length > 0) {
                this.logger.info(`📥 Найдено ${transactions.length} новых транзакций`);
                
                for (const tx of transactions) {
                    await this.processTransaction(tx);
                }
            }

            // Обновляем время последней синхронизации
            await this.updateLastSync();

        } catch (error) {
            this.logger.error('Ошибка синхронизации транзакций:', error);
        }
    }

    /**
     * Получение транзакций контракта из TON API
     */
    async getContractTransactions(fromLt = 0) {
        try {
            const response = await axios.get(`${this.tonApiUrl}/getTransactions`, {
                params: {
                    address: this.contractAddress,
                    limit: 100,
                    lt: fromLt,
                    hash: '',
                    to_lt: 0,
                    archival: false
                },
                headers: {
                    'X-API-Key': this.tonApiKey
                }
            });

            if (!response.data.ok) {
                throw new Error(`TON API error: ${response.data.error}`);
            }

            return response.data.result || [];
        } catch (error) {
            this.logger.error('Ошибка получения транзакций из TON API:', error);
            return [];
        }
    }

    /**
     * Обработка отдельной транзакции
     */
    async processTransaction(tx) {
        try {
            const txHash = tx.transaction_id.hash;
            const txLt = parseInt(tx.transaction_id.lt);

            // Проверяем, не обработана ли уже эта транзакция
            const existingTx = await this.pool.query(
                'SELECT id FROM marketplace_transactions WHERE tx_hash = $1',
                [txHash]
            );

            if (existingTx.rows.length > 0) {
                return; // Транзакция уже обработана
            }

            // Анализируем тип транзакции по входящим сообщениям
            const inMsg = tx.in_msg;
            if (!inMsg || !inMsg.msg_data || !inMsg.msg_data.body) {
                return; // Нет данных для анализа
            }

            const messageBody = inMsg.msg_data.body;
            const fromAddress = inMsg.source;
            const amount = parseInt(inMsg.value) || 0;

            // Парсим тип операции из тела сообщения
            const operation = this.parseOperationType(messageBody);
            
            if (!operation) {
                return; // Неизвестная операция
            }

            // Сохраняем транзакцию в базу
            await this.saveTransaction({
                txHash,
                txLt,
                txType: operation.type,
                fromAddress,
                toAddress: this.contractAddress,
                amountNanoton: amount,
                amountTon: amount / 1000000000, // Конвертируем в TON
                sellerID: operation.sellerID,
                productID: operation.productID,
                processed: false
            });

            // Обрабатываем операцию в зависимости от типа
            await this.handleOperation(operation, {
                txHash,
                txLt,
                fromAddress,
                amount
            });

        } catch (error) {
            this.logger.error(`Ошибка обработки транзакции ${tx.transaction_id.hash}:`, error);
        }
    }

    /**
     * Парсинг типа операции из тела сообщения
     */
    parseOperationType(messageBody) {
        try {
            // В реальной реализации здесь должен быть парсинг BOC (Bag of Cells)
            // Для примера используем простой текстовый формат
            
            if (typeof messageBody === 'string') {
                // Формат: "purchase:sellerID:productID" или "registerSeller:sellerID"
                const parts = messageBody.split(':');
                
                if (parts[0] === 'purchase' && parts.length >= 3) {
                    return {
                        type: 'purchase',
                        sellerID: parts[1],
                        productID: parts[2]
                    };
                }
                
                if (parts[0] === 'registerSeller' && parts.length >= 2) {
                    return {
                        type: 'register_seller',
                        sellerID: parts[1]
                    };
                }
            }

            return null;
        } catch (error) {
            this.logger.error('Ошибка парсинга операции:', error);
            return null;
        }
    }

    /**
     * Обработка операции в зависимости от типа
     */
    async handleOperation(operation, txData) {
        try {
            switch (operation.type) {
                case 'purchase':
                    await this.handlePurchase(operation, txData);
                    break;
                    
                case 'register_seller':
                    await this.handleSellerRegistration(operation, txData);
                    break;
                    
                default:
                    this.logger.warn(`Неизвестный тип операции: ${operation.type}`);
            }

            // Отмечаем транзакцию как обработанную
            await this.markTransactionProcessed(txData.txHash);

        } catch (error) {
            this.logger.error('Ошибка обработки операции:', error);
            
            // Сохраняем ошибку в базу
            await this.saveTransactionError(txData.txHash, error.message);
        }
    }

    /**
     * Обработка покупки товара
     */
    async handlePurchase(operation, txData) {
        const { sellerID, productID } = operation;
        const { txHash, fromAddress, amount } = txData;

        // Проверяем, существует ли продавец и товар
        const productResult = await this.pool.query(`
            SELECT p.*, s.wallet_address as seller_wallet
            FROM products p
            JOIN sellers s ON p.seller_id = s.seller_id
            WHERE p.seller_id = $1 AND p.product_id = $2 AND p.is_active = true
        `, [sellerID, productID]);

        if (productResult.rows.length === 0) {
            throw new Error(`Товар ${productID} продавца ${sellerID} не найден`);
        }

        const product = productResult.rows[0];
        const sellerShare = Math.floor(amount * 0.9); // 90%
        const serviceFee = amount - sellerShare; // 10%

        // Создаем заказ
        const orderID = `order_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        
        await this.pool.query(`
            INSERT INTO orders (
                order_id, buyer_wallet, seller_id, product_id, 
                price_ton, total_amount_ton, seller_share_ton, service_fee_ton,
                status, payment_status, purchase_tx_hash, purchase_tx_lt
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        `, [
            orderID, fromAddress, sellerID, productID,
            amount / 1000000000, amount / 1000000000, sellerShare / 1000000000, serviceFee / 1000000000,
            'confirmed', 'paid', txHash, txData.txLt
        ]);

        // Обновляем баланс продавца
        await this.pool.query(`
            INSERT INTO seller_balances (seller_id, product_id, balance_nanoton, balance_ton)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (seller_id, product_id) 
            DO UPDATE SET 
                balance_nanoton = seller_balances.balance_nanoton + $3,
                balance_ton = seller_balances.balance_ton + $4,
                last_updated = CURRENT_TIMESTAMP
        `, [sellerID, productID, sellerShare, sellerShare / 1000000000]);

        this.logger.info(`✅ Обработана покупка: ${orderID}, товар: ${productID}, сумма: ${amount / 1000000000} TON`);
    }

    /**
     * Обработка регистрации продавца
     */
    async handleSellerRegistration(operation, txData) {
        const { sellerID } = operation;
        const { txHash, fromAddress } = txData;

        // Проверяем, не зарегистрирован ли уже продавец
        const existingSeller = await this.pool.query(
            'SELECT id FROM sellers WHERE seller_id = $1 OR wallet_address = $2',
            [sellerID, fromAddress]
        );

        if (existingSeller.rows.length > 0) {
            this.logger.warn(`Продавец ${sellerID} уже зарегистрирован`);
            return;
        }

        // Регистрируем продавца
        await this.pool.query(`
            INSERT INTO sellers (seller_id, wallet_address, registration_tx_hash)
            VALUES ($1, $2, $3)
        `, [sellerID, fromAddress, txHash]);

        this.logger.info(`✅ Зарегистрирован новый продавец: ${sellerID} (${fromAddress})`);
    }

    /**
     * Сохранение транзакции в базу данных
     */
    async saveTransaction(txData) {
        await this.pool.query(`
            INSERT INTO marketplace_transactions (
                tx_hash, tx_lt, tx_type, from_address, to_address,
                amount_nanoton, amount_ton, seller_id, product_id, processed
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
            txData.txHash, txData.txLt, txData.txType, txData.fromAddress, txData.toAddress,
            txData.amountNanoton, txData.amountTon, txData.sellerID, txData.productID, txData.processed
        ]);
    }

    /**
     * Отметка транзакции как обработанной
     */
    async markTransactionProcessed(txHash) {
        await this.pool.query(
            'UPDATE marketplace_transactions SET processed = true WHERE tx_hash = $1',
            [txHash]
        );
    }

    /**
     * Сохранение ошибки обработки транзакции
     */
    async saveTransactionError(txHash, errorMessage) {
        await this.pool.query(
            'UPDATE marketplace_transactions SET error_message = $1 WHERE tx_hash = $2',
            [errorMessage, txHash]
        );
    }

    /**
     * Обновление времени последней синхронизации
     */
    async updateLastSync() {
        await this.pool.query(`
            UPDATE marketplace_contract_settings 
            SET last_sync = CURRENT_TIMESTAMP 
            WHERE contract_address = $1
        `, [this.contractAddress]);
    }

    /**
     * Получение баланса продавца из смарт-контракта
     */
    async getSellerBalance(sellerAddress) {
        try {
            // Здесь должен быть вызов get-метода смарт-контракта
            // Для примера возвращаем данные из базы
            const result = await this.pool.query(`
                SELECT SUM(balance_nanoton) as total_balance
                FROM seller_balances sb
                JOIN sellers s ON sb.seller_id = s.seller_id
                WHERE s.wallet_address = $1
            `, [sellerAddress]);

            return result.rows[0]?.total_balance || 0;
        } catch (error) {
            this.logger.error('Ошибка получения баланса продавца:', error);
            return 0;
        }
    }
}

module.exports = TonService;
