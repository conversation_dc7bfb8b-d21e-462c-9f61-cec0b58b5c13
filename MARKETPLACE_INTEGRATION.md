# 🛒 Интеграция смарт-контракта Marketplace.tact с бэкендом

## 📋 Обзор

Данная документация описывает пошаговую интеграцию смарт-контракта `Marketplace.tact` с существующим Node.js бэкендом для создания полноценного маркетплейса на блокчейне TON.

## 🏗️ Архитектура решения

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API    │    │  TON Blockchain │
│                 │    │                  │    │                 │
│ - Покупатели    │◄──►│ - Express.js     │◄──►│ - Marketplace   │
│ - Продавцы      │    │ - PostgreSQL     │    │   Smart Contract│
│ - TON Connect   │    │ - TON Service    │    │ - Транзакции    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Пошаговая инструкция по внедрению

### Шаг 1: Подготовка смарт-контракта

1. **Исправленный контракт**: Используйте файл `Marketplace_fixed.tact` вместо оригинального
2. **Компиляция**: 
   ```bash
   tact compile Marketplace_fixed.tact
   ```
3. **Деплой**: Разверните контракт в тестовой сети TON
4. **Сохраните адрес**: Запишите адрес развернутого контракта

### Шаг 2: Настройка базы данных

1. **Создание таблиц**:
   ```bash
   node run_sql.js sql/create_marketplace_tables.sql
   ```

2. **Проверка создания**:
   ```sql
   \dt -- Список всех таблиц
   ```

### Шаг 3: Установка зависимостей

Добавьте в `package.json` (если еще не установлены):
```bash
npm install axios
```

### Шаг 4: Настройка переменных окружения

1. **Скопируйте файл настроек**:
   ```bash
   cp .env.example .env
   ```

2. **Заполните переменные в `.env`**:
   ```env
   # Обязательно укажите адрес контракта после деплоя
   MARKETPLACE_CONTRACT_ADDRESS=EQC...ваш_адрес_контракта
   
   # Получите API ключ на https://toncenter.com
   TON_API_KEY=ваш_api_ключ
   ```

### Шаг 5: Запуск сервера

```bash
npm start
```

Проверьте логи:
- ✅ Должно появиться сообщение о мониторинге контракта
- ⚠️ Если адрес не установлен, будет предупреждение

## 📡 API Endpoints

### Продавцы

#### Регистрация продавца
```http
POST /api/marketplace/sellers/register
Content-Type: application/json

{
  "walletAddress": "EQC...",
  "sellerID": "flowershop7",
  "displayName": "Цветочный магазин",
  "description": "Свежие цветы с доставкой",
  "contactInfo": {
    "telegram": "@flowershop",
    "email": "<EMAIL>"
  }
}
```

#### Получение информации о продавце
```http
GET /api/marketplace/sellers/flowershop7
```

### Товары

#### Добавление товара
```http
POST /api/marketplace/products
Content-Type: application/json

{
  "walletAddress": "EQC...",
  "sellerID": "flowershop7",
  "productID": "roses_red_small",
  "title": "Красные розы (букет 5 шт)",
  "description": "Свежие красные розы",
  "priceTon": 2.5,
  "category": "flowers",
  "images": ["https://example.com/roses1.jpg"],
  "stockQuantity": 10
}
```

#### Получение товаров продавца
```http
GET /api/marketplace/sellers/flowershop7/products
```

#### Получение конкретного товара
```http
GET /api/marketplace/products/flowershop7/roses_red_small
```

## 🔄 Процесс покупки

### 1. Покупатель выбирает товар
- Получает информацию через API: `GET /api/marketplace/products/sellerID/productID`
- В ответе содержится `purchaseInfo` с инструкциями для покупки

### 2. Покупатель отправляет транзакцию
```javascript
// Пример для TON Connect
const transaction = {
  to: "EQC...адрес_контракта",
  value: "2500000000", // 2.5 TON в нанотонах
  data: {
    type: "comment",
    comment: "purchase:flowershop7:roses_red_small"
  }
};
```

### 3. Автоматическая обработка
- TON Service мониторит транзакции контракта
- При обнаружении покупки создается заказ в БД
- Обновляется баланс продавца
- Отправляются уведомления через WebSocket

## 💰 Вывод средств продавцом

### 1. Проверка баланса
```http
GET /api/marketplace/sellers/flowershop7
```

### 2. Вызов withdraw в смарт-контракте
```javascript
// Вывод по конкретному товару
const transaction = {
  to: "EQC...адрес_контракта",
  value: "50000000", // Gas fee
  data: {
    type: "comment", 
    comment: "withdraw:roses_red_small"
  }
};

// Вывод всех средств
const transaction = {
  to: "EQC...адрес_контракта",
  value: "50000000", // Gas fee
  data: {
    type: "comment",
    comment: "withdraw:"
  }
};
```

## 🔧 Мониторинг и отладка

### Логи сервера
```bash
tail -f service.log
```

### Проверка транзакций в БД
```sql
SELECT * FROM marketplace_transactions ORDER BY created_at DESC LIMIT 10;
```

### Проверка балансов
```sql
SELECT s.seller_id, s.display_name, 
       SUM(sb.balance_ton) as total_balance
FROM sellers s
LEFT JOIN seller_balances sb ON s.seller_id = sb.seller_id
GROUP BY s.seller_id, s.display_name;
```

## 🛠️ Расширение функционала

### Добавление новых типов операций

1. **Расширьте парсер в `tonService.js`**:
```javascript
parseOperationType(messageBody) {
  // Добавьте новые типы операций
  if (parts[0] === 'newOperation') {
    return { type: 'new_operation', ... };
  }
}
```

2. **Добавьте обработчик**:
```javascript
async handleOperation(operation, txData) {
  switch (operation.type) {
    case 'new_operation':
      await this.handleNewOperation(operation, txData);
      break;
  }
}
```

### Интеграция с существующими функциями

Маркетплейс можно интегрировать с существующей системой донатов:

```javascript
// В обработчике покупки
if (productID === 'donation_time') {
  // Интеграция с системой управления временем
  await this.handleTimeControlPurchase(sellerID, amount);
}
```

## 🔒 Безопасность

1. **Валидация адресов**: Всегда проверяйте TON адреса
2. **Авторизация**: Используйте подпись сообщений для авторизации
3. **Лимиты**: Установите лимиты на операции
4. **Мониторинг**: Отслеживайте подозрительную активность

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи сервера
2. Убедитесь, что контракт развернут и адрес указан правильно
3. Проверьте подключение к TON API
4. Убедитесь, что все таблицы БД созданы

## 🎯 Следующие шаги

1. **Тестирование**: Протестируйте все операции в тестовой сети
2. **UI/UX**: Создайте интерфейс для продавцов и покупателей  
3. **Уведомления**: Добавьте email/Telegram уведомления
4. **Аналитика**: Внедрите систему аналитики продаж
5. **Масштабирование**: Оптимизируйте для высоких нагрузок
