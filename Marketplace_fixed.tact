//////////////////////////////////////////////////////////////////////
//                                                             //
//  Smart Contract "Marketplace" for TON-based Trading Platform  //
//                                                             //
//  Функционал:                                                 //
//  - Регистрация продавцов (sellerID → TON-адрес)              //
//  - Прием платежей от покупателей за товары/услуги           //
//  - Распределение средств: 90% — продавцу, 10% — сервису      //
//  - Хранение балансов по каждому товару у каждого продавца    //
//  - Вывод средств продавцом (по товару или общая сумма)       //
//  - Вывод комиссионных средств сервисом                      //
//  - Перевод "просроченных" балансов сервису после года        //
//                                                             //
//////////////////////////////////////////////////////////////////////

contract Marketplace {

    //───────────────────────────────────────────────────────
    //                       Storage
    //───────────────────────────────────────────────────────

    /// Привязка sellerID (string) → TON-адрес продавца
    sellerRegistry: map<string, address>;

    /// Обратная привязка: адрес продавца → sellerID
    reverseRegistry: map<address, string>;

    /// Балансы по товарам у продавцов:
    /// ключ — (sellerID, productID) (оба строки), значение — накопленная сумма в нанотонах TON
    productBalances: map<(string, string), uint64>;

    /// Общие накопленные средства продавца (аналогично сумме всех productBalances для удобства вывода «все сразу»)
    sellerTotals: map<address, uint64>;

    /// Временные метки последнего "действия" (регистрации / покупки / вывода) продавца:
    /// ключ — адрес продавца, значение — Unix-timestamp последней активности
    lastActivity: map<address, uint64>;

    /// Баланс сервиса (накопленная комиссия 10%) в нанотонах TON
    serviceBalance: uint64 = 0;

    /// Владелец контракта (устанавливается при деплое)
    owner: address;

    //───────────────────────────────────────────────────────
    //                     Конструктор
    //───────────────────────────────────────────────────────

    /// При деплое контракта устанавливаем владельца
    init() {
        self.owner = msg.sender;
    }

    //───────────────────────────────────────────────────────
    //                Функции контракта (API)
    //───────────────────────────────────────────────────────

    /// 1) Регистрация продавца
    func registerSeller(sellerID: ^string) impure {
        // Проверка формата: sellerID не может быть пустой строкой
        require(sellerID.len() > 0, 1001, "sellerID не может быть пустым");

        // Проверяем, что sellerID ещё не занят
        if (self.sellerRegistry.contains(sellerID)) {
            throw(1002);  // Код 1002: sellerID уже зарегистрирован
        }

        // Записываем привязку sellerID → адрес вызывающего
        self.sellerRegistry.set(sellerID, msg.sender);
        
        // Записываем обратную привязку адрес → sellerID
        self.reverseRegistry.set(msg.sender, sellerID);

        // Устанавливаем начальную метку активности (считаем, что регистрация = активность)
        self.lastActivity.set(msg.sender, now());
    }

    /// 2) Покупка товара у продавца
    func purchase(sellerID: ^string, productID: ^string) impure {
        let amount: uint64 = msg.value;

        // Проверить, что продавец с таким sellerID зарегистрирован
        require(self.sellerRegistry.contains(sellerID), 2001, "sellerID не найден");

        // Извлечь адрес продавца
        let sellerAddress = self.sellerRegistry.get(sellerID);

        // Проверить, что buyer не отправляет 0 TON
        require(amount > 0, 2002, "Сумма должна быть > 0");

        // Вычисляем долю продавца (90%) и сервиса (10%)
        let sellerShare: uint64 = (amount * 90) / 100;
        let serviceShare: uint64 = amount - sellerShare;

        // Ключ для productBalances
        let key: (string, string) = (sellerID, productID);

        // Текущий баланс данного товара у продавца (если пустой, получим 0)
        let currentProductBalance: uint64 = self.productBalances.get(key) ?? 0u64;

        // Обновляем баланс товара
        self.productBalances.set(key, currentProductBalance + sellerShare);

        // Обновляем общий баланс продавца
        let currentTotal: uint64 = self.sellerTotals.get(sellerAddress) ?? 0u64;
        self.sellerTotals.set(sellerAddress, currentTotal + sellerShare);

        // Обновляем баланс сервиса
        self.serviceBalance += serviceShare;

        // Обновляем метку активности продавца
        self.lastActivity.set(sellerAddress, now());
    }

    /// 3) Вывод средств продавцом
    func withdraw(productID: ^string) impure {
        let caller: address = msg.sender;

        // Проверяем, что caller зарегистрирован как продавец
        if (!self.reverseRegistry.contains(caller)) {
            throw(3001); // "Вы не зарегистрированы как продавец"
        }

        // Определяем sellerID по адресу
        let sellerID: string = self.reverseRegistry.get(caller);

        // Если задан непустой productID — выводим только по нему
        if (productID.len() > 0) {
            let key: (string, string) = (sellerID, productID);
            let balanceProd: uint64 = self.productBalances.get(key) ?? 0u64;
            require(balanceProd > 0, 3002, "Баланс по этому товару = 0");

            // Отправляем всю сумму balanceProd
            send(caller, balanceProd);

            // Обнуляем баланс по этому товару
            self.productBalances.set(key, 0u64);

            // Обновляем общий баланс продавца
            let totalLeft: uint64 = self.sellerTotals.get(caller)! - balanceProd;
            self.sellerTotals.set(caller, totalLeft);

            // Обновляем метку активности
            self.lastActivity.set(caller, now());
        }
        else {
            // Если productID пустой — выводим весь balance (sellerTotals)
            let totalBalance: uint64 = self.sellerTotals.get(caller) ?? 0u64;
            require(totalBalance > 0, 3003, "Общий баланс = 0");

            send(caller, totalBalance);

            // Обнуляем sellerTotals
            self.sellerTotals.set(caller, 0u64);

            self.lastActivity.set(caller, now());
        }
    }

    /// 4) Вывод средств сервиса (только владелец контракта)
    func withdrawService(amount: uint64) impure {
        if (msg.sender != self.owner) {
            throw(4001); // "Только владелец сервиса может вызвать эту функцию"
        }

        require(self.serviceBalance >= amount, 4002, "Недостаточно средств сервиса");

        self.serviceBalance -= amount;
        send(self.owner, amount);
    }

    /// 5) Перевод "просроченных" средств сервиса
    func reclaimStaleFunds(sellerAddr: address) impure {
        // Проверка, что вызывает владелец контракта
        if (msg.sender != self.owner) {
            throw(5001); // "Только владелец сервиса может вызывать reclaimStaleFunds"
        }

        // Проверяем, что у продавца был хотя бы один активный action
        require(self.lastActivity.contains(sellerAddr), 5002, "Нет активности от этого продавца");

        let lastTime: uint64 = self.lastActivity.get(sellerAddr)!;
        let currentTime: uint64 = now();

        // 365 дней в секундах (365 * 24 * 60 * 60)
        let oneYear: uint64 = 31_536_000u64;

        require(currentTime > lastTime + oneYear, 5003, "Еще не прошло 365 дней");

        // Переводим весь баланс продавца в сервис
        let staleBalance: uint64 = self.sellerTotals.get(sellerAddr) ?? 0u64;
        if (staleBalance > 0) {
            self.serviceBalance += staleBalance;
            self.sellerTotals.set(sellerAddr, 0u64);
        }

        // Обновляем метку активности
        self.lastActivity.set(sellerAddr, currentTime);
    }

    //───────────────────────────────────────────────────────
    //                    Вспомогательные функции
    //───────────────────────────────────────────────────────

    /// 6) Получить баланс по конкретному товару
    func getProductBalance(sellerID: ^string, productID: ^string): uint64 {
        let key: (string, string) = (sellerID, productID);
        return self.productBalances.get(key) ?? 0u64;
    }

    /// 7) Получить общий баланс продавца (sellerTotals)
    func getSellerTotal(sellerAddr: address): uint64 {
        return self.sellerTotals.get(sellerAddr) ?? 0u64;
    }

    /// 8) Получить текущий баланс сервиса
    func getServiceBalance(): uint64 {
        return self.serviceBalance;
    }

    /// 9) Получить sellerID по адресу
    func getSellerID(sellerAddr: address): string? {
        return self.reverseRegistry.get(sellerAddr);
    }

    /// 10) Получить адрес продавца по sellerID
    func getSellerAddress(sellerID: ^string): address? {
        return self.sellerRegistry.get(sellerID);
    }
}
