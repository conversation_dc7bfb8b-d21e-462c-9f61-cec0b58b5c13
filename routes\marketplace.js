/**
 * API маршруты для маркетплейса
 */

const express = require('express');
const router = express.Router();

/**
 * Инициализация маршрутов маркетплейса
 */
function initMarketplaceRoutes(pool, logger, tonService) {

    // ==========================================
    // ПРОДАВЦЫ (Sellers)
    // ==========================================

    /**
     * Регистрация продавца
     * POST /api/marketplace/sellers/register
     */
    router.post('/sellers/register', async (req, res) => {
        const { walletAddress, sellerID, displayName, description, contactInfo } = req.body;

        if (!walletAddress || !sellerID) {
            return res.status(400).json({
                success: false,
                message: 'Адрес кошелька и ID продавца обязательны'
            });
        }

        try {
            // Проверяем уникальность sellerID и walletAddress
            const existingCheck = await pool.query(`
                SELECT seller_id, wallet_address FROM sellers 
                WHERE seller_id = $1 OR wallet_address = $2
            `, [sellerID, walletAddress]);

            if (existingCheck.rows.length > 0) {
                const existing = existingCheck.rows[0];
                if (existing.seller_id === sellerID) {
                    return res.status(409).json({
                        success: false,
                        message: `ID продавца "${sellerID}" уже занят`
                    });
                }
                if (existing.wallet_address === walletAddress) {
                    return res.status(409).json({
                        success: false,
                        message: 'Этот кошелек уже зарегистрирован как продавец'
                    });
                }
            }

            // Сохраняем продавца в базу (без registration_tx_hash - он будет добавлен после транзакции)
            const result = await pool.query(`
                INSERT INTO sellers (seller_id, wallet_address, display_name, description, contact_info)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING *
            `, [sellerID, walletAddress, displayName, description, JSON.stringify(contactInfo || {})]);

            const seller = result.rows[0];

            res.json({
                success: true,
                message: 'Продавец зарегистрирован в базе данных. Теперь выполните транзакцию registerSeller в смарт-контракте.',
                seller: {
                    id: seller.id,
                    sellerID: seller.seller_id,
                    walletAddress: seller.wallet_address,
                    displayName: seller.display_name,
                    description: seller.description
                },
                contractCall: {
                    method: 'registerSeller',
                    params: [sellerID],
                    note: `Вызовите registerSeller("${sellerID}") в смарт-контракте`
                }
            });

        } catch (error) {
            logger.error('Ошибка регистрации продавца:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера при регистрации продавца'
            });
        }
    });

    /**
     * Получение информации о продавце
     * GET /api/marketplace/sellers/:sellerID
     */
    router.get('/sellers/:sellerID', async (req, res) => {
        const { sellerID } = req.params;

        try {
            const result = await pool.query(`
                SELECT s.*, 
                       COUNT(p.id) as products_count,
                       COUNT(o.id) as orders_count,
                       COALESCE(SUM(sb.balance_ton), 0) as total_balance_ton
                FROM sellers s
                LEFT JOIN products p ON s.seller_id = p.seller_id AND p.is_active = true
                LEFT JOIN orders o ON s.seller_id = o.seller_id
                LEFT JOIN seller_balances sb ON s.seller_id = sb.seller_id
                WHERE s.seller_id = $1
                GROUP BY s.id
            `, [sellerID]);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Продавец не найден'
                });
            }

            const seller = result.rows[0];
            res.json({
                success: true,
                seller: {
                    id: seller.id,
                    sellerID: seller.seller_id,
                    walletAddress: seller.wallet_address,
                    displayName: seller.display_name,
                    description: seller.description,
                    avatarUrl: seller.avatar_url,
                    contactInfo: seller.contact_info,
                    isActive: seller.is_active,
                    stats: {
                        productsCount: parseInt(seller.products_count),
                        ordersCount: parseInt(seller.orders_count),
                        totalBalanceTon: parseFloat(seller.total_balance_ton)
                    },
                    createdAt: seller.created_at
                }
            });

        } catch (error) {
            logger.error('Ошибка получения информации о продавце:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера'
            });
        }
    });

    /**
     * Обновление информации о продавце
     * PUT /api/marketplace/sellers/:sellerID
     */
    router.put('/sellers/:sellerID', async (req, res) => {
        const { sellerID } = req.params;
        const { walletAddress, displayName, description, avatarUrl, contactInfo } = req.body;

        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Адрес кошелька обязателен для авторизации'
            });
        }

        try {
            // Проверяем, что продавец существует и кошелек совпадает
            const sellerCheck = await pool.query(
                'SELECT id FROM sellers WHERE seller_id = $1 AND wallet_address = $2',
                [sellerID, walletAddress]
            );

            if (sellerCheck.rows.length === 0) {
                return res.status(403).json({
                    success: false,
                    message: 'Доступ запрещен или продавец не найден'
                });
            }

            // Обновляем информацию
            await pool.query(`
                UPDATE sellers 
                SET display_name = $1, description = $2, avatar_url = $3, 
                    contact_info = $4, updated_at = CURRENT_TIMESTAMP
                WHERE seller_id = $5 AND wallet_address = $6
            `, [displayName, description, avatarUrl, JSON.stringify(contactInfo || {}), sellerID, walletAddress]);

            res.json({
                success: true,
                message: 'Информация о продавце обновлена'
            });

        } catch (error) {
            logger.error('Ошибка обновления информации о продавце:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера'
            });
        }
    });

    // ==========================================
    // ТОВАРЫ (Products)
    // ==========================================

    /**
     * Добавление товара
     * POST /api/marketplace/products
     */
    router.post('/products', async (req, res) => {
        const {
            walletAddress, sellerID, productID, title, description,
            priceTon, category, images, stockQuantity, digitalContent
        } = req.body;

        if (!walletAddress || !sellerID || !productID || !title || !priceTon) {
            return res.status(400).json({
                success: false,
                message: 'Обязательные поля: walletAddress, sellerID, productID, title, priceTon'
            });
        }

        try {
            // Проверяем, что продавец существует и кошелек совпадает
            const sellerCheck = await pool.query(
                'SELECT id FROM sellers WHERE seller_id = $1 AND wallet_address = $2',
                [sellerID, walletAddress]
            );

            if (sellerCheck.rows.length === 0) {
                return res.status(403).json({
                    success: false,
                    message: 'Доступ запрещен или продавец не найден'
                });
            }

            // Проверяем уникальность productID у данного продавца
            const productCheck = await pool.query(
                'SELECT id FROM products WHERE seller_id = $1 AND product_id = $2',
                [sellerID, productID]
            );

            if (productCheck.rows.length > 0) {
                return res.status(409).json({
                    success: false,
                    message: `Товар с ID "${productID}" уже существует у этого продавца`
                });
            }

            // Добавляем товар
            const result = await pool.query(`
                INSERT INTO products (
                    product_id, seller_id, title, description, price_ton,
                    category, images, stock_quantity, digital_content
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            `, [
                productID, sellerID, title, description, parseFloat(priceTon),
                category, JSON.stringify(images || []), stockQuantity || -1,
                JSON.stringify(digitalContent || {})
            ]);

            const product = result.rows[0];

            res.json({
                success: true,
                message: 'Товар успешно добавлен',
                product: {
                    id: product.id,
                    productID: product.product_id,
                    sellerID: product.seller_id,
                    title: product.title,
                    description: product.description,
                    priceTon: parseFloat(product.price_ton),
                    category: product.category,
                    images: product.images,
                    stockQuantity: product.stock_quantity,
                    isActive: product.is_active,
                    createdAt: product.created_at
                }
            });

        } catch (error) {
            logger.error('Ошибка добавления товара:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера при добавлении товара'
            });
        }
    });

    /**
     * Получение товаров продавца
     * GET /api/marketplace/sellers/:sellerID/products
     */
    router.get('/sellers/:sellerID/products', async (req, res) => {
        const { sellerID } = req.params;
        const { category, active = 'true' } = req.query;

        try {
            let query = `
                SELECT p.*, 
                       COUNT(o.id) as orders_count,
                       COALESCE(SUM(o.total_amount_ton), 0) as total_sales_ton
                FROM products p
                LEFT JOIN orders o ON p.seller_id = o.seller_id AND p.product_id = o.product_id
                WHERE p.seller_id = $1
            `;
            const params = [sellerID];

            if (active === 'true') {
                query += ' AND p.is_active = true';
            }

            if (category) {
                params.push(category);
                query += ` AND p.category = $${params.length}`;
            }

            query += ' GROUP BY p.id ORDER BY p.created_at DESC';

            const result = await pool.query(query, params);

            const products = result.rows.map(product => ({
                id: product.id,
                productID: product.product_id,
                sellerID: product.seller_id,
                title: product.title,
                description: product.description,
                priceTon: parseFloat(product.price_ton),
                category: product.category,
                images: product.images,
                stockQuantity: product.stock_quantity,
                isActive: product.is_active,
                stats: {
                    ordersCount: parseInt(product.orders_count),
                    totalSalesTon: parseFloat(product.total_sales_ton)
                },
                createdAt: product.created_at
            }));

            res.json({
                success: true,
                products
            });

        } catch (error) {
            logger.error('Ошибка получения товаров продавца:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера'
            });
        }
    });

    /**
     * Получение конкретного товара
     * GET /api/marketplace/products/:sellerID/:productID
     */
    router.get('/products/:sellerID/:productID', async (req, res) => {
        const { sellerID, productID } = req.params;

        try {
            const result = await pool.query(`
                SELECT p.*, s.display_name as seller_name, s.wallet_address as seller_wallet,
                       COUNT(o.id) as orders_count,
                       COALESCE(SUM(o.total_amount_ton), 0) as total_sales_ton
                FROM products p
                JOIN sellers s ON p.seller_id = s.seller_id
                LEFT JOIN orders o ON p.seller_id = o.seller_id AND p.product_id = o.product_id
                WHERE p.seller_id = $1 AND p.product_id = $2
                GROUP BY p.id, s.display_name, s.wallet_address
            `, [sellerID, productID]);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Товар не найден'
                });
            }

            const product = result.rows[0];

            res.json({
                success: true,
                product: {
                    id: product.id,
                    productID: product.product_id,
                    sellerID: product.seller_id,
                    sellerName: product.seller_name,
                    sellerWallet: product.seller_wallet,
                    title: product.title,
                    description: product.description,
                    priceTon: parseFloat(product.price_ton),
                    category: product.category,
                    images: product.images,
                    stockQuantity: product.stock_quantity,
                    digitalContent: product.digital_content,
                    isActive: product.is_active,
                    stats: {
                        ordersCount: parseInt(product.orders_count),
                        totalSalesTon: parseFloat(product.total_sales_ton)
                    },
                    createdAt: product.created_at,
                    purchaseInfo: {
                        contractAddress: process.env.MARKETPLACE_CONTRACT_ADDRESS,
                        method: 'purchase',
                        params: [sellerID, productID],
                        amount: parseFloat(product.price_ton),
                        note: `Для покупки отправьте ${product.price_ton} TON с сообщением "purchase:${sellerID}:${productID}"`
                    }
                }
            });

        } catch (error) {
            logger.error('Ошибка получения товара:', error);
            res.status(500).json({
                success: false,
                message: 'Ошибка сервера'
            });
        }
    });

    return router;
}

module.exports = initMarketplaceRoutes;
